{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_badge_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\app_badge_plus-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_ios-1.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-19.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.12\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_ios-3.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_ios-2.15.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.12+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\razorpay_flutter-1.4.0\\\\", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "stripe_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\stripe_ios-11.5.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "app_badge_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\app_badge_plus-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_android-1.1.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-19.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.28\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.12\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_android-3.3.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_android-4.6.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_android-2.16.2\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.12+23\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\razorpay_flutter-1.4.0\\\\", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "stripe_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\stripe_android-11.5.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.16\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "app_badge_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\app_badge_plus-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.2.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_macos-1.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-19.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_macos-3.1.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications_linux-6.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_linux-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.13.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_windows-0.6.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications_windows-1.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_windows-3.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_windows-0.2.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.23.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging_web-3.10.6\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_inappwebview_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_web-1.1.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_web-1.2.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_timezone-4.1.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.12\\\\", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_web-4.1.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_web-0.5.12+2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-3.0.6\\\\", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.3.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.4.1\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_badge_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_inappwebview_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux", "flutter_local_notifications_windows"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_local_notifications_windows", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "flutter_stripe", "dependencies": ["stripe_android", "stripe_ios"]}, {"name": "flutter_timezone", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "razorpay_flutter", "dependencies": ["fluttertoast"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "stripe_android", "dependencies": []}, {"name": "stripe_ios", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-13 17:07:54.077543", "version": "3.32.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}