1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37
38    <queries>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
39        <intent>
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
41        </intent>
42        <!-- Added to check the default browser that will host the AuthFlow. -->
43        <intent>
43-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
45
46            <data android:scheme="http" />
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
47        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
48        <package android:name="com.android.chrome" />
48-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
48-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
49
50        <intent>
50-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
51            <action android:name="android.intent.action.VIEW" />
51-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
51-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
52
53            <data
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
54                android:mimeType="*/*"
55                android:scheme="*" />
55-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
56        </intent>
57        <intent>
57-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
58            <action android:name="android.intent.action.VIEW" />
58-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
58-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
59
60            <category android:name="android.intent.category.BROWSABLE" />
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
61
62            <data
62-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
63                android:host="pay"
64                android:mimeType="*/*"
65                android:scheme="upi" />
65-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
66        </intent>
67        <intent>
67-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
68            <action android:name="android.intent.action.MAIN" />
68-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
68-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
69        </intent>
70        <intent>
70-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
71            <action android:name="android.intent.action.SEND" />
71-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
71-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
72
73            <data android:mimeType="*/*" />
73-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
74        </intent>
75        <intent>
75-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
76            <action android:name="rzp.device_token.share" />
76-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
76-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
77        </intent> <!-- Needs to be explicitly declared on Android R+ -->
78        <package android:name="com.google.android.apps.maps" />
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
79    </queries> <!-- Required by older versions of Google Play services to create IID tokens -->
80    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
81
82    <uses-feature
82-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
83        android:glEsVersion="0x00020000"
83-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
84        android:required="true" />
84-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
85
86    <permission
86-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
87        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
87-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
88        android:protectionLevel="signature" />
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
89
90    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
90-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
90-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
91
92    <application
93        android:name="android.app.Application"
94        android:allowBackup="false"
95        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
96        android:debuggable="true"
97        android:extractNativeLibs="true"
98        android:fullBackupContent="false"
99        android:icon="@mipmap/ic_launcher"
100        android:label="Label StoreMax"
101        android:usesCleartextTraffic="true" >
102        <activity
103            android:name="com.velvete.ly.MainActivity"
104            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
105            android:exported="true"
106            android:hardwareAccelerated="true"
107            android:launchMode="singleTop"
108            android:screenOrientation="portrait"
109            android:theme="@style/LaunchTheme"
110            android:windowSoftInputMode="adjustResize" >
111            <meta-data
112                android:name="io.flutter.embedding.android.NormalTheme"
113                android:resource="@style/NormalTheme" />
114
115            <intent-filter>
116                <action android:name="android.intent.action.MAIN" />
116-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
116-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
117
118                <category android:name="android.intent.category.LAUNCHER" />
119            </intent-filter>
120        </activity>
121
122        <meta-data
123            android:name="flutterEmbedding"
124            android:value="2" />
125        <meta-data
126            android:name="com.google.android.geo.API_KEY"
127            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
128
129        <service
129-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
130            android:name="com.baseflow.geolocator.GeolocatorLocationService"
130-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
131            android:enabled="true"
131-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
132            android:exported="false"
132-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
133            android:foregroundServiceType="location" />
133-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
134
135        <provider
135-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
136            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
136-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
137            android:authorities="com.velvete.ly.flutter.image_provider"
137-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
138            android:exported="false"
138-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
139            android:grantUriPermissions="true" >
139-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
140            <meta-data
140-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
142                android:resource="@xml/flutter_image_picker_file_paths" />
142-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
143        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
144        <service
144-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
145            android:name="com.google.android.gms.metadata.ModuleDependencies"
145-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
146            android:enabled="false"
146-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
147            android:exported="false" >
147-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
148            <intent-filter>
148-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
149                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
149-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
149-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
150            </intent-filter>
151
152            <meta-data
152-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
153                android:name="photopicker_activity:0:required"
153-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
154                android:value="" />
154-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
155        </service>
156
157        <activity
157-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
158            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
158-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
159            android:exported="false"
159-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
160            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
160-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
161
162        <service
162-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
163            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
163-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
164            android:exported="false"
164-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
165            android:permission="android.permission.BIND_JOB_SERVICE" />
165-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
166        <service
166-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
167            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
167-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
168            android:exported="false" >
168-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
169            <intent-filter>
169-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
170                <action android:name="com.google.firebase.MESSAGING_EVENT" />
170-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
170-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
171            </intent-filter>
172        </service>
173
174        <receiver
174-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
175-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
176            android:exported="true"
176-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
177            android:permission="com.google.android.c2dm.permission.SEND" >
177-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
178            <intent-filter>
178-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
179                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
179-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
179-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
180            </intent-filter>
181        </receiver>
182
183        <service
183-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
184            android:name="com.google.firebase.components.ComponentDiscoveryService"
184-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
185            android:directBootAware="true"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
186            android:exported="false" >
186-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
187            <meta-data
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
188                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
188-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
190            <meta-data
190-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
191                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
191-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
193            <meta-data
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
194                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
194-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
196            <meta-data
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
197                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
200-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
202            <meta-data
202-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
203                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
203-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
206                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
206-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
208            <meta-data
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
209                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
209-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
211            <meta-data
211-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
212                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
212-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
214        </service>
215
216        <provider
216-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
217            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
217-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
218            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
218-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
219            android:exported="false"
219-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
220            android:initOrder="99" />
220-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
221
222        <activity
222-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
223            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
223-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
224            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
224-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
225            android:exported="false"
225-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
226            android:theme="@style/AppTheme" />
226-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
227        <activity
227-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
228            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
228-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
229            android:exported="false"
229-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
230            android:theme="@style/ThemeTransparent" />
230-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
231        <activity
231-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
232            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
232-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
233            android:exported="false"
233-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
234            android:theme="@style/ThemeTransparent" />
234-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
235        <activity
235-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
236            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
236-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
237            android:exported="false"
237-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
238            android:launchMode="singleInstance"
238-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
239            android:theme="@style/ThemeTransparent" />
239-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
240        <activity
240-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
241            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
241-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
242            android:exported="false"
242-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
243            android:launchMode="singleInstance"
243-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
244            android:theme="@style/ThemeTransparent" />
244-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
245
246        <receiver
246-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
247            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
247-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
248            android:enabled="true"
248-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
249            android:exported="false" />
249-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
250
251        <meta-data
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
252            android:name="io.flutter.embedded_views_preview"
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
253            android:value="true" />
253-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
254
255        <activity
255-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
256            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
256-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
257            android:exported="true"
257-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
258            android:launchMode="singleTask" >
258-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
259            <intent-filter>
259-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
260                <action android:name="android.intent.action.VIEW" />
260-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
260-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
261
262                <category android:name="android.intent.category.DEFAULT" />
262-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
262-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
263                <category android:name="android.intent.category.BROWSABLE" />
263-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
263-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
264
265                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
266                <data
266-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
267                    android:host="link-accounts"
268                    android:pathPrefix="/com.velvete.ly/authentication_return"
269                    android:scheme="stripe-auth" />
269-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
270
271                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
272                <data
272-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
273                    android:host="link-native-accounts"
274                    android:pathPrefix="/com.velvete.ly/authentication_return"
275                    android:scheme="stripe-auth" />
275-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
276
277                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
278                <data
278-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
279                    android:host="link-accounts"
280                    android:path="/com.velvete.ly/success"
281                    android:scheme="stripe-auth" />
281-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
282                <data
282-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
283                    android:host="link-accounts"
284                    android:path="/com.velvete.ly/cancel"
285                    android:scheme="stripe-auth" />
285-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
286
287                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
288                <data
288-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
289                    android:host="native-redirect"
290                    android:pathPrefix="/com.velvete.ly"
291                    android:scheme="stripe-auth" />
291-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
292
293                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
294                <data
294-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
295                    android:host="auth-redirect"
296                    android:pathPrefix="/com.velvete.ly"
297                    android:scheme="stripe" />
297-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
298            </intent-filter>
299        </activity>
300        <activity
300-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
301            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
301-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
302            android:exported="false"
302-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
303            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
303-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
304        <activity
304-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
305            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
305-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
306            android:exported="false"
306-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
307            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
307-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
308            android:windowSoftInputMode="adjustResize" />
308-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
309        <activity
309-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
310            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
310-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
311            android:exported="false"
311-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
312            android:theme="@style/StripePaymentSheetDefaultTheme" />
312-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
313        <activity
313-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
314            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
314-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
315            android:exported="false"
315-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
316            android:theme="@style/StripePaymentSheetDefaultTheme" />
316-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
317        <activity
317-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
318            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
318-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
319            android:exported="false"
319-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
320            android:theme="@style/StripePaymentSheetDefaultTheme" />
320-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
321        <activity
321-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
322            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
322-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
323            android:exported="false"
323-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
324            android:theme="@style/StripePaymentSheetDefaultTheme" />
324-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
325        <activity
325-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
326            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
326-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
327            android:exported="false"
327-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
328            android:theme="@style/StripePaymentSheetDefaultTheme" />
328-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
329        <activity
329-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
330            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
330-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
331            android:exported="false"
331-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
332            android:theme="@style/StripePaymentSheetDefaultTheme" />
332-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
333        <activity
333-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
334            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
334-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
335            android:exported="false"
335-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
336            android:theme="@style/StripePaymentSheetDefaultTheme" />
336-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
337        <activity
337-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
338            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
339            android:exported="false"
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
340            android:theme="@style/StripePayLauncherDefaultTheme" />
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
341        <activity
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
342            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
343            android:theme="@style/StripePaymentSheetDefaultTheme" />
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
344        <activity
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
345            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
345-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
346            android:theme="@style/StripePaymentSheetDefaultTheme" />
346-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
347        <activity
347-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
348            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
348-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
349            android:theme="@style/StripePaymentSheetDefaultTheme" />
349-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
350        <activity
350-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
351            android:name="com.stripe.android.link.LinkActivity"
351-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
352            android:autoRemoveFromRecents="true"
352-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
353            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
353-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
354            android:exported="false"
354-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
355            android:label="@string/stripe_link"
355-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
356            android:theme="@style/StripeLinkBaseTheme"
356-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
357            android:windowSoftInputMode="adjustResize" />
357-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
358        <activity
358-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
359            android:name="com.stripe.android.link.LinkForegroundActivity"
359-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
360            android:autoRemoveFromRecents="true"
360-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
361            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
361-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
362            android:launchMode="singleTop"
362-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
363            android:theme="@style/StripeTransparentTheme" />
363-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
364        <activity
364-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
365            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
365-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
366            android:autoRemoveFromRecents="true"
366-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
367            android:exported="true"
367-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
368            android:launchMode="singleInstance"
368-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
369            android:theme="@style/StripeTransparentTheme" >
369-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
370            <intent-filter>
370-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
371                <action android:name="android.intent.action.VIEW" />
371-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
371-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
372
373                <category android:name="android.intent.category.DEFAULT" />
373-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
373-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
374                <category android:name="android.intent.category.BROWSABLE" />
374-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
374-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
375
376                <data
376-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
377                    android:host="complete"
378                    android:path="/com.velvete.ly"
379                    android:scheme="link-popup" />
379-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
380            </intent-filter>
381        </activity>
382        <activity
382-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
383            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
383-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
384            android:exported="false"
384-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
385            android:theme="@style/StripePaymentSheetDefaultTheme" />
385-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
386        <activity
386-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
387            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
387-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
388            android:exported="false"
388-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
389            android:theme="@style/StripeDefaultTheme" />
389-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
390        <activity
390-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
391            android:name="com.stripe.android.view.PaymentRelayActivity"
391-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
392            android:exported="false"
392-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
393            android:theme="@style/StripeTransparentTheme" />
393-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
394        <!--
395        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
396        launched the browser Activity will also handle the return URL deep link.
397        -->
398        <activity
398-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
399            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
399-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
400            android:exported="false"
400-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
401            android:launchMode="singleTask"
401-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
402            android:theme="@style/StripeTransparentTheme" />
402-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
403        <activity
403-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
404            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
404-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
405            android:exported="true"
405-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
406            android:launchMode="singleTask"
406-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
407            android:theme="@style/StripeTransparentTheme" >
407-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
408            <intent-filter>
408-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
409                <action android:name="android.intent.action.VIEW" />
409-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
409-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
410
411                <category android:name="android.intent.category.DEFAULT" />
411-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
411-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
412                <category android:name="android.intent.category.BROWSABLE" />
412-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
412-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
413
414                <!-- Must match `DefaultReturnUrl#value`. -->
415                <data
415-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
416                    android:host="payment_return_url"
417                    android:path="/com.velvete.ly"
418                    android:scheme="stripesdk" />
418-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
419            </intent-filter>
420        </activity>
421        <activity
421-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
422            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
422-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
423            android:exported="false"
423-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
424            android:theme="@style/StripeDefaultTheme" />
424-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
425        <activity
425-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
426            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
426-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
427            android:exported="false"
427-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
428            android:theme="@style/StripeGooglePayDefaultTheme" />
428-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
429        <activity
429-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
430            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
430-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
431            android:exported="false"
431-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
432            android:theme="@style/StripeGooglePayDefaultTheme" />
432-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
433        <activity
433-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
434            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
434-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
435            android:exported="false"
435-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
436            android:theme="@style/StripePayLauncherDefaultTheme" />
436-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
437        <activity
437-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
438            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
438-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
439            android:exported="false"
439-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
440            android:theme="@style/StripeTransparentTheme" />
440-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
441        <activity
441-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
442            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
442-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
443            android:exported="false"
443-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
444            android:theme="@style/Stripe3DS2Theme" />
444-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
445        <activity
445-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
446            android:name="com.razorpay.CheckoutActivity"
446-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
447            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
447-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
448            android:exported="false"
448-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
449            android:theme="@style/CheckoutTheme" >
449-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
450            <intent-filter>
450-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
451                <action android:name="android.intent.action.MAIN" />
451-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
451-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
452            </intent-filter>
453        </activity>
454
455        <provider
455-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
456            android:name="androidx.startup.InitializationProvider"
456-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
457            android:authorities="com.velvete.ly.androidx-startup"
457-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
458            android:exported="false" >
458-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
459            <meta-data
459-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
460                android:name="com.razorpay.RazorpayInitializer"
460-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
461                android:value="androidx.startup" />
461-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
462            <meta-data
462-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
463                android:name="androidx.emoji2.text.EmojiCompatInitializer"
463-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
464                android:value="androidx.startup" />
464-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
465            <meta-data
465-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
466                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
466-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
467                android:value="androidx.startup" />
467-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
468            <meta-data
468-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
469                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
470                android:value="androidx.startup" />
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
471        </provider>
472
473        <activity
473-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
474            android:name="com.razorpay.MagicXActivity"
474-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
475            android:exported="false"
475-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
476            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
476-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
477
478        <meta-data
478-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
479            android:name="com.razorpay.plugin.googlepay_all"
479-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
480            android:value="com.razorpay.RzpGpayMerged" />
480-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
481
482        <activity
482-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
483            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
483-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
484            android:excludeFromRecents="true"
484-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
485            android:exported="false"
485-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
486            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
486-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
487        <!--
488            Service handling Google Sign-In user revocation. For apps that do not integrate with
489            Google Sign-In, this service will never be started.
490        -->
491        <service
491-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
492            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
492-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
493            android:exported="true"
493-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
494            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
494-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
495            android:visibleToInstantApps="true" />
495-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
496
497        <receiver
497-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
498            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
498-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
499            android:exported="true"
499-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
500            android:permission="com.google.android.c2dm.permission.SEND" >
500-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
501            <intent-filter>
501-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
502                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
502-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
502-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
503            </intent-filter>
504
505            <meta-data
505-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
506                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
506-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
507                android:value="true" />
507-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
508        </receiver>
509        <!--
510             FirebaseMessagingService performs security checks at runtime,
511             but set to not exported to explicitly avoid allowing another app to call it.
512        -->
513        <service
513-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
514            android:name="com.google.firebase.messaging.FirebaseMessagingService"
514-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
515            android:directBootAware="true"
515-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
516            android:exported="false" >
516-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
517            <intent-filter android:priority="-500" >
517-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
518                <action android:name="com.google.firebase.MESSAGING_EVENT" />
518-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
518-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
519            </intent-filter>
520        </service> <!-- Needs to be explicitly declared on P+ -->
521        <uses-library
521-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
522            android:name="org.apache.http.legacy"
522-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
523            android:required="false" />
523-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
524
525        <activity
525-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
526            android:name="com.google.android.gms.common.api.GoogleApiActivity"
526-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
527            android:exported="false"
527-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
528            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
528-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
529
530        <provider
530-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
531            android:name="com.google.firebase.provider.FirebaseInitProvider"
531-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
532            android:authorities="com.velvete.ly.firebaseinitprovider"
532-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
533            android:directBootAware="true"
533-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
534            android:exported="false"
534-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
535            android:initOrder="100" />
535-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
536
537        <uses-library
537-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
538            android:name="androidx.window.extensions"
538-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
539            android:required="false" />
539-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
540        <uses-library
540-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
541            android:name="androidx.window.sidecar"
541-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
542            android:required="false" />
542-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
543
544        <meta-data
544-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
545            android:name="com.google.android.gms.version"
545-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
546            android:value="@integer/google_play_services_version" />
546-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
547
548        <receiver
548-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
549            android:name="androidx.profileinstaller.ProfileInstallReceiver"
549-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
550            android:directBootAware="false"
550-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
551            android:enabled="true"
551-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
552            android:exported="true"
552-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
553            android:permission="android.permission.DUMP" >
553-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
554            <intent-filter>
554-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
555                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
555-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
555-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
556            </intent-filter>
557            <intent-filter>
557-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
558                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
558-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
558-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
559            </intent-filter>
560            <intent-filter>
560-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
561                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
561-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
561-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
562            </intent-filter>
563            <intent-filter>
563-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
564                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
564-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
564-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
565            </intent-filter>
566        </receiver>
567
568        <service
568-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
569            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
569-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
570            android:exported="false" >
570-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
571            <meta-data
571-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
572                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
572-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
573                android:value="cct" />
573-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
574        </service>
575        <service
575-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
576            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
576-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
577            android:exported="false"
577-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
578            android:permission="android.permission.BIND_JOB_SERVICE" >
578-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
579        </service>
580
581        <receiver
581-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
582            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
582-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
583            android:exported="false" />
583-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
584
585        <meta-data
585-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
586            android:name="aia-compat-api-min-version"
586-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
587            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
587-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
588        <activity
588-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
589            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
589-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
590            android:exported="false"
590-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
591            android:stateNotNeeded="true"
591-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
592            android:theme="@style/Theme.PlayCore.Transparent" />
592-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
593    </application>
594
595</manifest>
