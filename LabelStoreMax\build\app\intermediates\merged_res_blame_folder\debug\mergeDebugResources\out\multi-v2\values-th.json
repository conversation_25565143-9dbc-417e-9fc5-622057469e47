{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-105:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "69,77,148,152,475,481,482", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6729,7403,13013,13301,43570,44198,44278", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6794,7484,13088,13435,43733,44273,44351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,268,269,283,295,296,298,305,306,316,317,325,326", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15100,15357,15643,15804,15889,15956,16470,16535,16669,16734,16807,16879,16948,17009,17078,17201,17268,17516,18463,18528,19557,19707,19785,23838,23954,25234,26093,26140,26261,26954,27013,27799,27888,28570,28659", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "15160,15434,15706,15884,15951,16012,16530,16592,16729,16802,16874,16943,17004,17073,17137,17263,17327,17602,18523,18622,19628,19780,19865,23949,24019,25279,26135,26203,26320,27008,27085,27883,27968,28654,28739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13615", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "50,73,78", "startColumns": "4,4,4", "startOffsets": "4533,7100,7489", "endColumns": "71,76,71", "endOffsets": "4600,7172,7556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5575", "endColumns": "128", "endOffsets": "5699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3302,3370,3436,4010,4563,4638,4744,4838,4893,4980,5064,5130,5214,5297,5354,5428,5477,5560,5658,5727,5798,5865,5931,5976,6051,6145,6219,6269,6315,6385,6443,6508,6675,6828,6944,7009,7087,7157,7244,7321,7401,7470,7564,7634,7742,7822,7908,7958,8068,8116,8173,8246,8308,8376,8442,8514,8597,8661,8710", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3297,3365,3431,4005,4558,4633,4739,4833,4888,4975,5059,5125,5209,5292,5349,5423,5472,5555,5653,5722,5793,5860,5926,5971,6046,6140,6214,6264,6310,6380,6438,6503,6670,6823,6939,7004,7082,7152,7239,7316,7396,7465,7559,7629,7737,7817,7903,7953,8063,8111,8168,8241,8303,8371,8437,8509,8592,8656,8705,8784"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,236,242,243,244,255,256,257,258,259,260,261,262,263,264,265,266,273,274,275,278,279,280,281,285,290,291,292,293,294,299,300,301,302,303,304,308,309,319,320,322,323,327,328,330,335,342,343,414,415,416,418,423,436,437,438,439,440,441,442,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14104,14173,14251,14313,14381,14458,14520,14815,14870,14945,15021,15247,15439,15549,15711,16077,16378,17410,17681,17783,17854,18019,18113,18201,18376,18698,18797,18875,18981,19051,19140,19226,19309,19390,19489,19633,19870,19971,20744,20812,20878,22343,22896,22971,23077,23171,23226,23313,23397,23463,23547,23630,23687,24265,24314,24397,24653,24722,24793,24860,25375,25754,25829,25923,25997,26047,26325,26395,26453,26518,26685,26838,27143,27208,28098,28168,28329,28406,28744,28813,28975,29557,30102,30182,37464,37514,37624,37767,38336,40291,40353,40421,40487,40559,40642,40706,42041", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "14168,14246,14308,14376,14453,14515,14583,14865,14940,15016,15095,15352,15544,15638,15799,16160,16465,17511,17778,17849,17948,18108,18196,18312,18458,18792,18870,18976,19046,19135,19221,19304,19385,19484,19552,19702,19966,20060,20807,20873,21447,22891,22966,23072,23166,23221,23308,23392,23458,23542,23625,23682,23756,24309,24392,24490,24717,24788,24855,24921,25415,25824,25918,25992,26042,26088,26390,26448,26513,26680,26833,26949,27203,27281,28163,28250,28401,28481,28808,28902,29040,29660,30177,30263,37509,37619,37667,37819,38404,40348,40416,40482,40554,40637,40701,40750,42115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "479,480", "startColumns": "4,4", "startOffsets": "44006,44103", "endColumns": "96,94", "endOffsets": "44098,44193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4711,4858,4981,5088,5224,5348,5467,5704,5848,5953,6100,6222,6362,6513,6577,6645", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4706,4853,4976,5083,5219,5343,5462,5570,5843,5948,6095,6217,6357,6508,6572,6640,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "27286", "endColumns": "79", "endOffsets": "27361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,373,468,548,627,749,840,943,1036,1161,1220,1349,1416,1601,1794,1943,2031,2127,2220,2321,2467,2571,2668,2901,3016,3142,3328,3520,3615,3726,3907,3998,4056,4119,4202,4294,4394,4465,4558,4659,4763,4945,5012,5078,5153,5266,5344,5430,5493,5567,5641,5710,5820,5911,6018,6109,6179,6258,6326,6411,6471,6571,6677,6780,6903,6968,7059,7155,7297,7366,7451,7546,7600,7657,7740,7840,8001,8242,8498,8596,8673,8757,8830,8935,9045,9131,9205,9275,9359,9429,9520,9637,9762,9833,9904,9961,10138,10210,10272,10351,10424,10498,10573,10695,10805,10905,10991,11068,11151,11216", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "119,202,277,368,463,543,622,744,835,938,1031,1156,1215,1344,1411,1596,1789,1938,2026,2122,2215,2316,2462,2566,2663,2896,3011,3137,3323,3515,3610,3721,3902,3993,4051,4114,4197,4289,4389,4460,4553,4654,4758,4940,5007,5073,5148,5261,5339,5425,5488,5562,5636,5705,5815,5906,6013,6104,6174,6253,6321,6406,6466,6566,6672,6775,6898,6963,7054,7150,7292,7361,7446,7541,7595,7652,7735,7835,7996,8237,8493,8591,8668,8752,8825,8930,9040,9126,9200,9270,9354,9424,9515,9632,9757,9828,9899,9956,10133,10205,10267,10346,10419,10493,10568,10690,10800,10900,10986,11063,11146,11211,11349"}, "to": {"startLines": "168,169,170,240,252,253,254,271,284,286,287,318,336,338,341,345,346,347,350,352,354,355,356,357,358,359,360,361,362,363,364,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,413,417,427,428,429,430,431,432,433,434,435,444,445,446,447,448,449,450,451,452,453,454,455,456,457,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14588,14657,14740,20438,22089,22184,22264,24079,25284,25420,25523,27973,29665,29783,30035,30330,30515,30708,30999,31182,31377,31470,31571,31717,31821,31918,32151,32266,32392,32578,32770,32948,33059,33240,33331,33389,33452,33535,33627,33727,33798,33891,33992,34096,34278,34345,34411,34486,34599,34677,34763,34826,34900,34974,35043,35630,35721,35828,35919,35989,36068,36136,36221,36281,36381,36487,36590,36713,36778,36869,36965,37107,37379,37672,39164,39218,39275,39358,39458,39619,39860,40116,40214,40810,40894,40967,41072,41182,41268,41342,41412,41496,41566,41657,41774,41899,41970,42120,42177,42354,42426,42488,42567,42640,42714,42789,42911,43021,43121,43207,43284,43367,43432", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "14652,14735,14810,20524,22179,22259,22338,24196,25370,25518,25611,28093,29719,29907,30097,30510,30703,30852,31082,31273,31465,31566,31712,31816,31913,32146,32261,32387,32573,32765,32860,33054,33235,33326,33384,33447,33530,33622,33722,33793,33886,33987,34091,34273,34340,34406,34481,34594,34672,34758,34821,34895,34969,35038,35148,35716,35823,35914,35984,36063,36131,36216,36276,36376,36482,36585,36708,36773,36864,36960,37102,37171,37459,37762,39213,39270,39353,39453,39614,39855,40111,40209,40286,40889,40962,41067,41177,41263,41337,41407,41491,41561,41652,41769,41894,41965,42036,42172,42349,42421,42483,42562,42635,42709,42784,42906,43016,43116,43202,43279,43362,43427,43565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,326,428,643,692,760,849,920,1132,1192,1280,1357,1412,1476,1547,1634,1942,2010,2080,2133,2186,2263,2378,2474,2542,2619,2693,2777,2845,2941,3205,3279,3357,3416,3478,3539,3601,3668,3743,3838,3937,4020,4139,4242,4315,4394,4497,4700,4912,5029,5158,5212,5808,5905,5967", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "161,321,423,638,687,755,844,915,1127,1187,1275,1352,1407,1471,1542,1629,1937,2005,2075,2128,2181,2258,2373,2469,2537,2614,2688,2772,2840,2936,3200,3274,3352,3411,3473,3534,3596,3663,3738,3833,3932,4015,4134,4237,4310,4389,4492,4695,4907,5024,5153,5207,5803,5900,5962,6017"}, "to": {"startLines": "237,238,239,241,245,246,247,248,249,250,251,267,270,272,276,277,282,288,289,297,307,311,312,313,314,315,321,324,329,331,332,333,334,337,339,340,344,348,349,351,353,365,390,391,392,393,394,412,419,420,421,422,424,425,426,443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20065,20176,20336,20529,21452,21501,21569,21658,21729,21941,22001,23761,24024,24201,24495,24566,24926,25616,25684,26208,27090,27366,27443,27558,27654,27722,28255,28486,28907,29045,29141,29405,29479,29724,29912,29974,30268,30857,30924,31087,31278,32865,35153,35272,35375,35448,35527,37176,37824,38036,38153,38282,38409,39005,39102,40755", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "20171,20331,20433,20739,21496,21564,21653,21724,21936,21996,22084,23833,24074,24260,24561,24648,25229,25679,25749,26256,27138,27438,27553,27649,27717,27794,28324,28565,28970,29136,29400,29474,29552,29778,29969,30030,30325,30919,30994,31177,31372,32943,35267,35370,35443,35522,35625,37374,38031,38148,38277,38331,39000,39097,39159,40805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,7265,7333,7561,8020,8260,8320,8407,8473,8538,8599,8663,8724,8778,8879,8940,9000,9054,9124,9235,9322,9399,9486,9568,9649,9792,9871,9953,10085,10177,10255,10309,10362,10428,10498,10576,10647,10727,10799,10877,10946,11015,11113,11195,11283,11376,11470,11544,11613,11708,11760,11843,11911,11996,12084,12146,12210,12273,12343,12443,12539,12636,12729,12787,13224,13697,13779,13927", "endLines": "5,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,7328,7398,7646,8085,8315,8402,8468,8533,8594,8658,8719,8773,8874,8935,8995,9049,9119,9230,9317,9394,9481,9563,9644,9787,9866,9948,10080,10172,10250,10304,10357,10423,10493,10571,10642,10722,10794,10872,10941,11010,11108,11190,11278,11371,11465,11539,11608,11703,11755,11838,11906,11991,12079,12141,12205,12268,12338,12438,12534,12631,12724,12782,12839,13296,13774,13849,13998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "83,483", "startColumns": "4,4", "startOffsets": "7959,44356", "endColumns": "60,74", "endOffsets": "8015,44426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "48,49,71,72,74,85,86,146,147,149,150,153,154,158,476,477,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4456,6902,6999,7177,8090,8175,12844,12930,13093,13158,13440,13526,13854,43738,43816,43883", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4451,4528,6994,7095,7260,8170,8255,12925,13008,13153,13219,13521,13610,13922,43811,43878,44001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "70,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "6799,7651,7750,7861", "endColumns": "102,98,110,97", "endOffsets": "6897,7745,7856,7954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15165,16017,16165,16229,16314,16597,17142,17332,17607,17953,18317,18627", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "15242,16072,16224,16309,16373,16664,17196,17405,17676,18014,18371,18693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,14003", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,14099"}}]}]}