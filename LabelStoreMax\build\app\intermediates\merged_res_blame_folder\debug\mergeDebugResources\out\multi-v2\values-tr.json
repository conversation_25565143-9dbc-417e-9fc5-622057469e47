{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-105:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,207,280,386,470,551,632,763,869,964,1059,1189,1252,1368,1430,1611,1811,1962,2049,2150,2243,2339,2502,2608,2708,2954,3066,3196,3403,3617,3713,3827,4041,4140,4200,4263,4351,4449,4542,4616,4714,4811,4916,5124,5203,5270,5344,5463,5545,5630,5703,5782,5851,5924,6041,6128,6240,6336,6409,6496,6576,6663,6723,6829,6934,7045,7175,7240,7332,7431,7562,7634,7726,7826,7885,7942,8021,8126,8297,8557,8842,8943,9016,9107,9179,9291,9409,9498,9585,9658,9746,9826,9917,10047,10198,10275,10349,10412,10591,10657,10718,10799,10873,10945,11034,11175,11300,11415,11499,11581,11667,11736", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "120,202,275,381,465,546,627,758,864,959,1054,1184,1247,1363,1425,1606,1806,1957,2044,2145,2238,2334,2497,2603,2703,2949,3061,3191,3398,3612,3708,3822,4036,4135,4195,4258,4346,4444,4537,4611,4709,4806,4911,5119,5198,5265,5339,5458,5540,5625,5698,5777,5846,5919,6036,6123,6235,6331,6404,6491,6571,6658,6718,6824,6929,7040,7170,7235,7327,7426,7557,7629,7721,7821,7880,7937,8016,8121,8292,8552,8837,8938,9011,9102,9174,9286,9404,9493,9580,9653,9741,9821,9912,10042,10193,10270,10344,10407,10586,10652,10713,10794,10868,10940,11029,11170,11295,11410,11494,11576,11662,11731,11892"}, "to": {"startLines": "175,176,177,247,259,260,261,278,291,293,294,325,343,345,348,352,353,354,357,359,361,362,363,364,365,366,367,368,369,370,371,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,420,424,434,435,436,437,438,439,440,441,442,451,452,453,454,455,456,457,458,459,460,461,462,463,464,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15393,15463,15545,21150,22910,22994,23075,24985,26259,26412,26507,29039,30837,30962,31198,31492,31673,31873,32167,32353,32547,32640,32736,32899,33005,33105,33351,33463,33593,33800,34014,34210,34324,34538,34637,34697,34760,34848,34946,35039,35113,35211,35308,35413,35621,35700,35767,35841,35960,36042,36127,36200,36279,36348,36421,36974,37061,37173,37269,37342,37429,37509,37596,37656,37762,37867,37978,38108,38173,38265,38364,38495,38778,39097,40792,40851,40908,40987,41092,41263,41523,41808,41909,42517,42608,42680,42792,42910,42999,43086,43159,43247,43327,43418,43548,43699,43776,43926,43989,44168,44234,44295,44376,44450,44522,44611,44752,44877,44992,45076,45158,45244,45313", "endColumns": "69,81,72,105,83,80,80,130,105,94,94,129,62,115,61,180,199,150,86,100,92,95,162,105,99,245,111,129,206,213,95,113,213,98,59,62,87,97,92,73,97,96,104,207,78,66,73,118,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,130,71,91,99,58,56,78,104,170,259,284,100,72,90,71,111,117,88,86,72,87,79,90,129,150,76,73,62,178,65,60,80,73,71,88,140,124,114,83,81,85,68,160", "endOffsets": "15458,15540,15613,21251,22989,23070,23151,25111,26360,26502,26597,29164,30895,31073,31255,31668,31868,32019,32249,32449,32635,32731,32894,33000,33100,33346,33458,33588,33795,34009,34105,34319,34533,34632,34692,34755,34843,34941,35034,35108,35206,35303,35408,35616,35695,35762,35836,35955,36037,36122,36195,36274,36343,36416,36533,37056,37168,37264,37337,37424,37504,37591,37651,37757,37862,37973,38103,38168,38260,38359,38490,38562,38865,39192,40846,40903,40982,41087,41258,41518,41803,41904,41977,42603,42675,42787,42905,42994,43081,43154,43242,43322,43413,43543,43694,43771,43845,43984,44163,44229,44290,44371,44445,44517,44606,44747,44872,44987,45071,45153,45239,45308,45469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5700", "endColumns": "146", "endOffsets": "5842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,7466,7526,7758,8229,8489,8550,8641,8704,8767,8826,8895,8958,9012,9120,9178,9240,9294,9367,9488,9572,9652,9751,9835,9926,10066,10143,10219,10350,10437,10513,10566,10620,10686,10756,10833,10904,10984,11055,11130,11208,11279,11380,11465,11554,11649,11742,11814,11886,11982,12034,12120,12187,12271,12361,12423,12487,12550,12620,12714,12816,12905,13005,13062,13504,13963,14047,14193", "endLines": "5,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,7521,7585,7844,8301,8545,8636,8699,8762,8821,8890,8953,9007,9115,9173,9235,9289,9362,9483,9567,9647,9746,9830,9921,10061,10138,10214,10345,10432,10508,10561,10615,10681,10751,10828,10899,10979,11050,11125,11203,11274,11375,11460,11549,11644,11737,11809,11881,11977,12029,12115,12182,12266,12356,12418,12482,12545,12615,12709,12811,12900,13000,13057,13115,13578,14042,14117,14262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,239,308,377,440,511", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "135,234,303,372,435,506,573"}, "to": {"startLines": "161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14368,14453,14552,14621,14690,14753,14824", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "14448,14547,14616,14685,14748,14819,14886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,345,431,491,557,609,689,765,846,933,1035,1122,1204,1288,1373,1476,1572,1641,1734,1821,1900,2006,2092,2185,2261,2361,2439,2536,2623,2706,2789,2887,2963,3046,3155,3257,3326,3393,4055,4693,4770,4876,4975,5030,5114,5198,5266,5355,5447,5502,5582,5630,5706,5812,5881,5951,6018,6084,6131,6214,6311,6387,6433,6481,6563,6621,6688,6862,7016,7145,7213,7293,7367,7454,7534,7616,7687,7775,7843,7942,8023,8113,8168,8291,8340,8397,8462,8524,8594,8661,8730,8817,8885,8936", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "121,201,263,340,426,486,552,604,684,760,841,928,1030,1117,1199,1283,1368,1471,1567,1636,1729,1816,1895,2001,2087,2180,2256,2356,2434,2531,2618,2701,2784,2882,2958,3041,3150,3252,3321,3388,4050,4688,4765,4871,4970,5025,5109,5193,5261,5350,5442,5497,5577,5625,5701,5807,5876,5946,6013,6079,6126,6209,6306,6382,6428,6476,6558,6616,6683,6857,7011,7140,7208,7288,7362,7449,7529,7611,7682,7770,7838,7937,8018,8108,8163,8286,8335,8392,8457,8519,8589,8656,8725,8812,8880,8931,9007"}, "to": {"startLines": "168,169,170,171,172,173,174,178,179,180,181,184,186,187,189,194,198,213,216,217,218,220,221,222,224,228,229,230,231,232,233,234,235,236,237,239,242,243,249,250,251,262,263,264,265,266,267,268,269,270,271,272,273,280,281,282,285,286,287,288,292,297,298,299,300,301,306,307,308,309,310,311,315,316,326,327,329,330,334,335,337,342,349,350,421,422,423,425,430,443,444,445,446,447,448,449,465", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14891,14962,15042,15104,15181,15267,15327,15618,15670,15750,15826,16048,16213,16315,16470,16832,17122,18151,18411,18507,18576,18732,18819,18898,19066,19365,19458,19534,19634,19712,19809,19896,19979,20062,20160,20310,20557,20666,21480,21549,21616,23156,23794,23871,23977,24076,24131,24215,24299,24367,24456,24548,24603,25180,25228,25304,25578,25647,25717,25784,26365,26742,26825,26922,26998,27044,27331,27413,27471,27538,27712,27866,28188,28256,29169,29243,29406,29486,29841,29912,30072,30738,31260,31341,38870,38925,39048,39197,39799,41982,42044,42114,42181,42250,42337,42405,43850", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,75,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "14957,15037,15099,15176,15262,15322,15388,15665,15745,15821,15902,16130,16310,16397,16547,16911,17202,18249,18502,18571,18664,18814,18893,18999,19147,19453,19529,19629,19707,19804,19891,19974,20057,20155,20231,20388,20661,20763,21544,21611,22273,23789,23866,23972,24071,24126,24210,24294,24362,24451,24543,24598,24678,25223,25299,25405,25642,25712,25779,25845,26407,26820,26917,26993,27039,27087,27408,27466,27533,27707,27861,27990,28251,28331,29238,29325,29481,29563,29907,29995,30135,30832,31336,31426,38920,39043,39092,39249,39859,42039,42109,42176,42245,42332,42400,42451,43921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,13883", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,13958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,14267", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,14363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "28336", "endColumns": "84", "endOffsets": "28416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "48,49,71,72,74,85,86,146,147,149,150,153,154,158,483,484,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4427,4520,7106,7201,7382,8306,8389,13120,13208,13370,13438,13715,13795,14122,45643,45721,45789", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "4515,4599,7196,7296,7461,8384,8484,13203,13287,13433,13499,13790,13878,14188,45716,45784,45902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "70,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7006,7849,7955,8062", "endColumns": "99,105,106,105", "endOffsets": "7101,7950,8057,8163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,193,255,338,399,474,531,610,682,745,807", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "128,188,250,333,394,469,526,605,677,740,802,871"}, "to": {"startLines": "183,193,195,196,197,201,209,212,215,219,223,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15970,16772,16916,16978,17061,17342,17882,18072,18339,18669,19004,19296", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "16043,16827,16973,17056,17117,17412,17934,18146,18406,18727,19061,19360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,80", "endOffsets": "130,211,292"}, "to": {"startLines": "50,73,78", "startColumns": "4,4,4", "startOffsets": "4604,7301,7677", "endColumns": "79,80,80", "endOffsets": "4679,7377,7753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "83,490", "startColumns": "4,4", "startOffsets": "8168,46239", "endColumns": "60,78", "endOffsets": "8224,46313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,341,437,661,706,776,865,937,1152,1212,1293,1358,1413,1477,1553,1645,2003,2077,2143,2196,2252,2326,2448,2559,2616,2692,2768,2853,2925,3037,3364,3446,3523,3585,3645,3705,3766,3833,3909,4008,4101,4201,4301,4395,4468,4547,4637,4848,5072,5193,5337,5393,6156,6260,6321", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "177,336,432,656,701,771,860,932,1147,1207,1288,1353,1408,1472,1548,1640,1998,2072,2138,2191,2247,2321,2443,2554,2611,2687,2763,2848,2920,3032,3359,3441,3518,3580,3640,3700,3761,3828,3904,4003,4096,4196,4296,4390,4463,4542,4632,4843,5067,5188,5332,5388,6151,6255,6316,6377"}, "to": {"startLines": "244,245,246,248,252,253,254,255,256,257,258,274,277,279,283,284,289,295,296,304,314,318,319,320,321,322,328,331,336,338,339,340,341,344,346,347,351,355,356,358,360,372,397,398,399,400,401,419,426,427,428,429,431,432,433,450", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20768,20895,21054,21256,22278,22323,22393,22482,22554,22769,22829,24683,24930,25116,25410,25486,25850,26602,26676,27214,28132,28421,28495,28617,28728,28785,29330,29568,30000,30140,30252,30579,30661,30900,31078,31138,31431,32024,32091,32254,32454,34110,36538,36638,36732,36805,36884,38567,39254,39478,39599,39743,39864,40627,40731,42456", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,75,91,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,103,60,60", "endOffsets": "20890,21049,21145,21475,22318,22388,22477,22549,22764,22824,22905,24743,24980,25175,25481,25573,26203,26671,26737,27262,28183,28490,28612,28723,28780,28856,29401,29648,30067,30247,30574,30656,30733,30957,31133,31193,31487,32086,32162,32348,32542,34205,36633,36727,36800,36879,36969,38773,39473,39594,39738,39794,40622,40726,40787,42512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,350,423,484,556,619,682,750,818,885,945,1020,1084,1154,1217,1302,1366,1446,1520,1599,1684,1787,1866,1917,1966,2039,2103,2167,2240,2336,2418,2512", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "113,191,259,345,418,479,551,614,677,745,813,880,940,1015,1079,1149,1212,1297,1361,1441,1515,1594,1679,1782,1861,1912,1961,2034,2098,2162,2235,2331,2413,2507,2601"}, "to": {"startLines": "182,185,188,190,191,192,199,200,202,203,204,205,206,207,208,210,211,214,225,226,238,240,241,275,276,290,302,303,305,312,313,323,324,332,333", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15907,16135,16402,16552,16638,16711,17207,17279,17417,17480,17548,17616,17683,17743,17818,17939,18009,18254,19152,19216,20236,20393,20472,24748,24851,26208,27092,27141,27267,27995,28059,28861,28957,29653,29747", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "15965,16208,16465,16633,16706,16767,17274,17337,17475,17543,17611,17678,17738,17813,17877,18004,18067,18334,19211,19291,20305,20467,20552,24846,24925,26254,27136,27209,27326,28054,28127,28952,29034,29742,29836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "69,77,148,152,482,488,489", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6935,7590,13292,13583,45474,46078,46161", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "7001,7672,13365,13710,45638,46156,46234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4794,4949,5085,5190,5337,5467,5594,5847,6019,6126,6283,6417,6562,6729,6791,6855", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4789,4944,5080,5185,5332,5462,5589,5695,6014,6121,6278,6412,6557,6724,6786,6850,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "486,487", "startColumns": "4,4", "startOffsets": "45907,45991", "endColumns": "83,86", "endOffsets": "45986,46073"}}]}]}