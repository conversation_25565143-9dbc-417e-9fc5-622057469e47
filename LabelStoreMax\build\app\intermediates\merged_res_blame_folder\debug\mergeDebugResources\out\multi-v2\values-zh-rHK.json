{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-105:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "70,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "6240,6992,7084,7185", "endColumns": "82,91,100,92", "endOffsets": "6318,7079,7180,7273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3856,3946,4053,6649,6700,6914,7339,7549,7607,7685,7746,7803,7859,7918,7976,8030,8116,8172,8230,8284,8349,8442,8516,8588,8668,8742,8820,8940,9003,9066,9165,9242,9316,9366,9417,9483,9547,9615,9686,9758,9819,9890,9957,10017,10105,10185,10248,10331,10416,10490,10555,10631,10679,10753,10817,10893,10971,11033,11097,11160,11226,11306,11386,11462,11543,11597,12000,12415,12490,12630", "endLines": "5,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,2916,2977,3044,3113,3190,3941,4048,4121,6695,6757,6987,7393,7602,7680,7741,7798,7854,7913,7971,8025,8111,8167,8225,8279,8344,8437,8511,8583,8663,8737,8815,8935,8998,9061,9160,9237,9311,9361,9412,9478,9542,9610,9681,9753,9814,9885,9952,10012,10100,10180,10243,10326,10411,10485,10550,10626,10674,10748,10812,10888,10966,11028,11092,11155,11221,11301,11381,11457,11538,11592,11647,12064,12485,12558,12695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "50,73,78", "startColumns": "4,4,4", "startOffsets": "4276,6502,6843", "endColumns": "71,68,70", "endOffsets": "4343,6566,6909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "182,185,188,190,191,192,199,200,202,203,204,205,206,207,208,210,211,214,225,226,238,240,241,275,276,290,302,303,305,312,313,322,323,331,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14141,14356,14584,14717,14790,14855,15305,15368,15491,15551,15619,15683,15744,15802,15868,15987,16052,16257,17021,17080,17931,18071,18136,21152,21224,22224,22936,22982,23095,23588,23641,24230,24302,24839,24909", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "14196,14422,14643,14785,14850,14911,15363,15424,15546,15614,15678,15739,15797,15863,15925,16047,16105,16319,17075,17145,17999,18131,18202,21219,21282,22264,22977,23039,23152,23636,23698,24297,24364,24904,24973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "183,193,195,196,197,201,209,212,215,219,223,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14201,14916,15043,15103,15176,15429,15930,16110,16324,16608,16894,17150", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "14267,14968,15098,15171,15229,15486,15982,16172,16385,16663,16946,17208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "83,489", "startColumns": "4,4", "startOffsets": "7278,37279", "endColumns": "60,71", "endOffsets": "7334,37346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3216,3294,3371,3437,3515,3592,3677,3798,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5486,5544,5621,5705,5806,5873,5936,6016,6066,6117,6175,6244,6362,6527,6710,6789,6849,6914,6973,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3211,3289,3366,3432,3510,3587,3672,3793,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5481,5539,5616,5700,5801,5868,5931,6011,6061,6112,6170,6239,6357,6522,6705,6784,6844,6909,6968,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "175,176,177,247,259,260,261,278,291,293,294,324,342,344,347,351,352,353,356,358,360,361,362,363,364,365,366,367,368,369,370,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,419,423,433,434,435,436,437,438,439,440,441,450,451,452,453,454,455,456,457,458,459,460,461,462,463,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13708,13767,13833,18685,19803,19874,19947,21340,22269,22382,22453,24369,25669,25774,25962,26225,26343,26465,26687,26862,27021,27105,27191,27295,27383,27469,27588,27673,27772,27940,28107,28272,28362,28464,28543,28600,28659,28734,28812,28889,28955,29033,29110,29195,29316,29379,29441,29502,29592,29662,29735,29794,29863,29931,29994,30490,30571,30662,30743,30808,30879,30943,31014,31070,31153,31229,31313,31412,31470,31547,31631,31732,31900,32130,33028,33078,33129,33187,33256,33374,33539,33722,33801,34328,34393,34452,34533,34618,34691,34759,34824,34893,34956,35031,35109,35193,35258,35383,35433,35546,35611,35664,35732,35794,35872,35938,36023,36109,36187,36258,36325,36388,36448", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "13762,13828,13889,18749,19869,19942,20015,21413,22333,22448,22520,24443,25715,25841,26016,26338,26460,26555,26767,26939,27100,27186,27290,27378,27464,27583,27668,27767,27935,28102,28190,28357,28459,28538,28595,28654,28729,28807,28884,28950,29028,29105,29190,29311,29374,29436,29497,29587,29657,29730,29789,29858,29926,29989,30077,30566,30657,30738,30803,30874,30938,31009,31065,31148,31224,31308,31407,31465,31542,31626,31727,31794,31958,32205,33073,33124,33182,33251,33369,33534,33717,33796,33856,34388,34447,34528,34613,34686,34754,34819,34888,34951,35026,35104,35188,35253,35316,35428,35541,35606,35659,35727,35789,35867,35933,36018,36104,36182,36253,36320,36383,36443,36537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,12336", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,12410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "48,49,71,72,74,85,86,146,147,149,150,153,154,158,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4126,4202,6323,6411,6571,7398,7472,11652,11730,11874,11937,12188,12261,12563,36710,36785,36850", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4197,4271,6406,6497,6644,7467,7544,11725,11799,11932,11995,12256,12331,12625,36780,36845,36961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12801,12876,12960,13025,13091,13151,13213", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "12871,12955,13020,13086,13146,13208,13270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3287,3386,3480,3574,3667,3760,12700", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3282,3381,3475,3569,3662,3755,3851,12796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4348,4449,4577,4692,4794,4901,5017,5119,5320,5430,5531,5660,5775,5882,5990,6045,6102", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4444,4572,4687,4789,4896,5012,5114,5207,5425,5526,5655,5770,5877,5985,6040,6097,6169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4971,5014,5057,5112,5170,5228,5319,5411,5488,5548,5611,5670,5745,5808,5868,5930,6001,6059,6133,6202,6279,6328,6401,6446,6496,6552,6608,6669,6728,6789,6860,6919,6964", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4966,5009,5052,5107,5165,5223,5314,5406,5483,5543,5606,5665,5740,5803,5863,5925,5996,6054,6128,6197,6274,6323,6396,6441,6491,6547,6603,6664,6723,6784,6855,6914,6959,7021"}, "to": {"startLines": "168,169,170,171,172,173,174,178,179,180,181,184,186,187,189,194,198,213,216,217,218,220,221,222,224,228,229,230,231,232,233,234,235,236,237,239,242,243,249,250,251,262,263,264,265,266,267,268,269,270,271,272,273,280,281,282,285,286,287,288,292,297,298,299,300,301,306,307,308,309,310,311,315,316,325,326,328,329,333,334,336,341,348,349,420,421,422,424,429,442,443,444,445,446,447,448,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13275,13335,13403,13464,13525,13594,13648,13894,13942,14007,14073,14272,14427,14511,14648,14973,15234,16177,16390,16469,16532,16668,16741,16811,16951,17213,17289,17359,17442,17507,17582,17655,17724,17793,17871,18004,18207,18299,18866,18929,18990,20020,20337,20402,20486,20566,20621,20697,20769,20827,20898,20971,21026,21482,21527,21597,21824,21881,21946,22013,22338,22645,22709,22786,22850,22893,23157,23212,23270,23328,23419,23511,23754,23814,24448,24507,24649,24712,24978,25040,25171,25595,26021,26090,31963,32012,32085,32210,32604,33861,33917,33978,34037,34098,34169,34228,35321", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "13330,13398,13459,13520,13589,13643,13703,13937,14002,14068,14136,14351,14506,14579,14712,15038,15300,16252,16464,16527,16603,16736,16806,16889,17016,17284,17354,17437,17502,17577,17650,17719,17788,17866,17926,18066,18294,18379,18924,18985,19312,20332,20397,20481,20561,20616,20692,20764,20822,20893,20966,21021,21091,21522,21592,21674,21876,21941,22008,22075,22377,22704,22781,22845,22888,22931,23207,23265,23323,23414,23506,23583,23809,23872,24502,24577,24707,24767,25035,25106,25224,25664,26085,26162,32007,32080,32125,32255,32655,33912,33973,34032,34093,34164,34223,34268,35378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "485,486", "startColumns": "4,4", "startOffsets": "36966,37047", "endColumns": "80,76", "endOffsets": "37042,37119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5212", "endColumns": "107", "endOffsets": "5315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "69,77,148,152,481,487,488", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6174,6762,11804,12069,36542,37124,37203", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "6235,6838,11869,12183,36705,37198,37274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1192,1272,1416,1476,1536,1587,1638,1704,1788,1865,1920,1991,2058,2125,2185,2260,2413,2479,2551,2605,2663,2721,2779,2840,2906,2996,3073,3150,3240,3324,3394,3473,3558,3659,3768,3860,3954,4003,4239,4314,4371", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1187,1267,1411,1471,1531,1582,1633,1699,1783,1860,1915,1986,2053,2120,2180,2255,2408,2474,2546,2600,2658,2716,2774,2835,2901,2991,3068,3145,3235,3319,3389,3468,3553,3654,3763,3855,3949,3998,4234,4309,4366,4421"}, "to": {"startLines": "244,245,246,248,252,253,254,255,256,257,258,274,277,279,283,284,289,295,296,304,314,317,318,319,320,321,327,330,335,337,338,339,340,343,345,346,350,354,355,357,359,371,396,397,398,399,400,418,425,426,427,428,430,431,432,449", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18384,18473,18608,18754,19317,19361,19419,19494,19558,19676,19731,21096,21287,21418,21679,21744,22080,22525,22585,23044,23703,23877,23943,24027,24104,24159,24582,24772,25111,25229,25304,25457,25523,25720,25846,25904,26167,26560,26621,26772,26944,28195,30082,30172,30256,30326,30405,31799,32260,32369,32461,32555,32660,32896,32971,34273", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "18468,18603,18680,18861,19356,19414,19489,19553,19671,19726,19798,21147,21335,21477,21739,21819,22219,22580,22640,23090,23749,23938,24022,24099,24154,24225,24644,24834,25166,25299,25452,25518,25590,25769,25899,25957,26220,26616,26682,26857,27016,28267,30167,30251,30321,30400,30485,31895,32364,32456,32550,32599,32891,32966,33023,34323"}}]}]}