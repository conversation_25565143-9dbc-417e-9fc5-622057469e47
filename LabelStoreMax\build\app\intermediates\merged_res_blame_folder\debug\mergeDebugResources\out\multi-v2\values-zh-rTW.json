{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-105:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "48,49,71,72,74,85,86,146,147,149,150,153,154,158,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4138,4215,6332,6419,6580,7408,7482,11659,11737,11882,11947,12201,12274,12576,36727,36801,36869", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "4210,4286,6414,6505,6653,7477,7554,11732,11807,11942,12007,12269,12344,12639,36796,36864,36980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5227", "endColumns": "102", "endOffsets": "5325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "485,486", "startColumns": "4,4", "startOffsets": "36985,37068", "endColumns": "82,78", "endOffsets": "37063,37142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "183,193,195,196,197,201,209,212,215,219,223,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14215,14930,15057,15117,15190,15443,15944,16124,16338,16622,16918,17176", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "14281,14982,15112,15185,15243,15500,15996,16186,16399,16677,16970,17234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,12714", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,12810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4363,4464,4592,4707,4809,4916,5032,5132,5330,5440,5541,5670,5785,5887,5995,6051,6108", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4459,4587,4702,4804,4911,5027,5127,5222,5435,5536,5665,5780,5882,5990,6046,6103,6177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,245,306,375,429,489,537,602,668,736,820,904,977,1046,1116,1187,1267,1346,1409,1485,1558,1633,1721,1791,1867,1937,2020,2085,2160,2233,2302,2371,2453,2513,2580,2672,2757,2820,2881,3207,3508,3573,3657,3737,3792,3868,3940,3998,4069,4142,4197,4267,4312,4382,4464,4521,4586,4653,4720,4764,4828,4905,4969,5012,5055,5110,5168,5226,5317,5409,5486,5546,5609,5668,5743,5806,5866,5928,5999,6057,6131,6202,6279,6328,6403,6448,6498,6554,6610,6671,6730,6791,6862,6919,6964", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "110,178,240,301,370,424,484,532,597,663,731,815,899,972,1041,1111,1182,1262,1341,1404,1480,1553,1628,1716,1786,1862,1932,2015,2080,2155,2228,2297,2366,2448,2508,2575,2667,2752,2815,2876,3202,3503,3568,3652,3732,3787,3863,3935,3993,4064,4137,4192,4262,4307,4377,4459,4516,4581,4648,4715,4759,4823,4900,4964,5007,5050,5105,5163,5221,5312,5404,5481,5541,5604,5663,5738,5801,5861,5923,5994,6052,6126,6197,6274,6323,6398,6443,6493,6549,6605,6666,6725,6786,6857,6914,6959,7021"}, "to": {"startLines": "168,169,170,171,172,173,174,178,179,180,181,184,186,187,189,194,198,213,216,217,218,220,221,222,224,228,229,230,231,232,233,234,235,236,237,239,242,243,249,250,251,262,263,264,265,266,267,268,269,270,271,272,273,280,281,282,285,286,287,288,292,297,298,299,300,301,306,307,308,309,310,311,315,316,325,326,328,329,333,334,336,341,348,349,420,421,422,424,429,442,443,444,445,446,447,448,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13288,13348,13416,13478,13539,13608,13662,13908,13956,14021,14087,14286,14441,14525,14662,14987,15248,16191,16404,16483,16546,16682,16755,16830,16975,17239,17315,17385,17468,17533,17608,17681,17750,17819,17901,18034,18237,18329,18895,18958,19019,20046,20347,20412,20496,20576,20631,20707,20779,20837,20908,20981,21036,21493,21538,21608,21835,21892,21957,22024,22349,22656,22720,22797,22861,22904,23168,23223,23281,23339,23430,23522,23765,23825,24460,24519,24662,24725,24991,25053,25184,25608,26035,26106,31981,32030,32105,32230,32624,33881,33937,33998,34057,34118,34189,34246,35338", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "13343,13411,13473,13534,13603,13657,13717,13951,14016,14082,14150,14365,14520,14593,14726,15052,15314,16266,16478,16541,16617,16750,16825,16913,17040,17310,17380,17463,17528,17603,17676,17745,17814,17896,17956,18096,18324,18409,18953,19014,19340,20342,20407,20491,20571,20626,20702,20774,20832,20903,20976,21031,21101,21533,21603,21685,21887,21952,22019,22086,22388,22715,22792,22856,22899,22942,23218,23276,23334,23425,23517,23594,23820,23883,24514,24589,24720,24780,25048,25119,25237,25677,26101,26178,32025,32100,32145,32275,32675,33932,33993,34052,34113,34184,34241,34286,35395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3215,3293,3370,3436,3514,3591,3676,3797,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5487,5545,5622,5706,5807,5874,5937,6017,6067,6118,6176,6245,6363,6528,6711,6790,6850,6915,6974,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3210,3288,3365,3431,3509,3586,3671,3792,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5482,5540,5617,5701,5802,5869,5932,6012,6062,6113,6171,6240,6358,6523,6706,6785,6845,6910,6969,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "175,176,177,247,259,260,261,278,291,293,294,324,342,344,347,351,352,353,356,358,360,361,362,363,364,365,366,367,368,369,370,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,419,423,433,434,435,436,437,438,439,440,441,450,451,452,453,454,455,456,457,458,459,460,461,462,463,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13722,13781,13847,18714,19829,19900,19973,21351,22280,22393,22464,24381,25682,25787,25976,26241,26359,26481,26703,26878,27037,27121,27207,27311,27399,27485,27604,27689,27788,27956,28123,28288,28378,28480,28559,28616,28675,28749,28827,28904,28970,29048,29125,29210,29331,29395,29457,29518,29608,29678,29751,29810,29879,29947,30010,30507,30588,30679,30760,30825,30896,30960,31031,31087,31170,31246,31330,31430,31488,31565,31649,31750,31918,32150,33048,33098,33149,33207,33276,33394,33559,33742,33821,34346,34411,34470,34550,34635,34708,34776,34841,34910,34973,35048,35126,35210,35275,35400,35450,35563,35628,35681,35749,35811,35889,35955,36040,36126,36204,36275,36342,36405,36465", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "13776,13842,13903,18778,19895,19968,20041,21424,22344,22459,22531,24455,25728,25854,26030,26354,26476,26571,26783,26955,27116,27202,27306,27394,27480,27599,27684,27783,27951,28118,28206,28373,28475,28554,28611,28670,28744,28822,28899,28965,29043,29120,29205,29326,29390,29452,29513,29603,29673,29746,29805,29874,29942,30005,30093,30583,30674,30755,30820,30891,30955,31026,31082,31165,31241,31325,31425,31483,31560,31644,31745,31812,31976,32225,33093,33144,33202,33271,33389,33554,33737,33816,33876,34406,34465,34545,34630,34703,34771,34836,34905,34968,35043,35121,35205,35270,35333,35445,35558,35623,35676,35744,35806,35884,35950,36035,36121,36199,36270,36337,36400,36460,36554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,12349", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,12423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "125,209,274,340,400,462,523"}, "to": {"startLines": "161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12815,12890,12974,13039,13105,13165,13227", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "12885,12969,13034,13100,13160,13222,13283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "83,489", "startColumns": "4,4", "startOffsets": "7287,37302", "endColumns": "60,71", "endOffsets": "7343,37369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,6658,6709,6923,7348,7559,7619,7697,7758,7816,7872,7932,7990,8044,8129,8185,8243,8297,8362,8454,8528,8600,8682,8756,8833,8953,9016,9079,9178,9255,9329,9379,9430,9496,9559,9627,9698,9769,9830,9901,9968,10030,10117,10196,10261,10344,10429,10503,10567,10643,10691,10764,10828,10904,10982,11044,11108,11171,11237,11317,11395,11471,11550,11604,12012,12428,12503,12644", "endLines": "5,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,6704,6766,6996,7403,7614,7692,7753,7811,7867,7927,7985,8039,8124,8180,8238,8292,8357,8449,8523,8595,8677,8751,8828,8948,9011,9074,9173,9250,9324,9374,9425,9491,9554,9622,9693,9764,9825,9896,9963,10025,10112,10191,10256,10339,10424,10498,10562,10638,10686,10759,10823,10899,10977,11039,11103,11166,11232,11312,11390,11466,11545,11599,11654,12076,12498,12571,12709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,197", "endColumns": "71,69,70", "endOffsets": "122,192,263"}, "to": {"startLines": "50,73,78", "startColumns": "4,4,4", "startOffsets": "4291,6510,6852", "endColumns": "71,69,70", "endOffsets": "4358,6575,6918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "70,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "6248,7001,7093,7194", "endColumns": "83,91,100,92", "endOffsets": "6327,7088,7189,7282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "69,77,148,152,481,487,488", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6182,6771,11812,12081,36559,37147,37226", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "6243,6847,11877,12196,36722,37221,37297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1333,1406,1471,1542,1614,1677,1722,1768,1830,1892,1945,2007,2079,2146,2216", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1328,1401,1466,1537,1609,1672,1717,1763,1825,1887,1940,2002,2074,2141,2211,2280"}, "to": {"startLines": "182,185,188,190,191,192,199,200,202,203,204,205,206,207,208,210,211,214,225,226,238,240,241,275,276,290,302,303,305,312,313,322,323,331,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14155,14370,14598,14731,14804,14869,15319,15382,15505,15565,15633,15697,15758,15816,15882,16001,16066,16271,17045,17104,17961,18101,18166,21162,21234,22235,22947,22993,23106,23599,23652,24242,24314,24852,24922", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "14210,14436,14657,14799,14864,14925,15377,15438,15560,15628,15692,15753,15811,15877,15939,16061,16119,16333,17099,17171,18029,18161,18232,21229,21292,22275,22988,23050,23163,23647,23709,24309,24376,24917,24986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,355,467,511,569,644,708,824,879,951,1007,1061,1125,1190,1270,1414,1474,1534,1585,1636,1702,1786,1863,1918,1990,2058,2125,2185,2260,2413,2479,2551,2605,2663,2722,2780,2841,2907,2997,3074,3151,3242,3326,3396,3475,3560,3661,3770,3862,3956,4005,4241,4316,4373", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,350,462,506,564,639,703,819,874,946,1002,1056,1120,1185,1265,1409,1469,1529,1580,1631,1697,1781,1858,1913,1985,2053,2120,2180,2255,2408,2474,2546,2600,2658,2717,2775,2836,2902,2992,3069,3146,3237,3321,3391,3470,3555,3656,3765,3857,3951,4000,4236,4311,4368,4423"}, "to": {"startLines": "244,245,246,248,252,253,254,255,256,257,258,274,277,279,283,284,289,295,296,304,314,317,318,319,320,321,327,330,335,337,338,339,340,343,345,346,350,354,355,357,359,371,396,397,398,399,400,418,425,426,427,428,430,431,432,449", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18414,18503,18638,18783,19345,19389,19447,19522,19586,19702,19757,21106,21297,21429,21690,21755,22091,22536,22596,23055,23714,23888,23954,24038,24115,24170,24594,24785,25124,25242,25317,25470,25536,25733,25859,25917,26183,26576,26637,26788,26960,28211,30098,30189,30273,30343,30422,31817,32280,32389,32481,32575,32680,32916,32991,34291", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "18498,18633,18709,18890,19384,19442,19517,19581,19697,19752,19824,21157,21346,21488,21750,21830,22230,22591,22651,23101,23760,23949,24033,24110,24165,24237,24657,24847,25179,25312,25465,25531,25603,25782,25912,25971,26236,26632,26698,26873,27032,28283,30184,30268,30338,30417,30502,31913,32384,32476,32570,32619,32911,32986,33043,34341"}}]}]}