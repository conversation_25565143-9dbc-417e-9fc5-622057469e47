//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/events/logout_event.dart';
import '/app/services/woocommerce_customer_service.dart';
import '/app/services/auth_service.dart';
import '/app/models/woocommerce_wrappers/my_woo_customer.dart';
import '/resources/pages/wishlist_page_widget.dart';
import '/resources/pages/account_profile_update_page.dart';
import '/resources/pages/account_shipping_details_page.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/widgets/account_detail_orders_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:wp_json_api/exceptions/invalid_user_token_exception.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wp_json_api/models/responses/wc_customer_info_response.dart';
import 'package:wp_json_api/wp_json_api.dart';

class AccountDetailPage extends NyStatefulWidget {
  static RouteView path = ("/account-detail", (_) => AccountDetailPage());

  AccountDetailPage({super.key, this.showLeadingBackButton = true})
      : super(child: () => _AccountDetailPageState());
  final bool showLeadingBackButton;
}

class _AccountDetailPageState extends NyPage<AccountDetailPage> {
  WCCustomerInfoResponse? _wcCustomerInfoResponse;
  MyWooCustomer? _currentCustomer;
  final WooCommerceCustomerService _customerService = WooCommerceCustomerService();

  // Real settings state variables
  bool _notificationsEnabled = false;
  bool _isDarkMode = false;
  String _currentLanguage = 'العربية';
  bool _isLoggedIn = false;

  @override
  get init => () async {
        print('🌐 ===== PROFILE PAGE: INITIALIZATION =====');
        print('📍 Creating TabController and fetching user data');
        print('===============================================');

        try {
          await _fetchWpUserData();
          await _loadRealSettings();

          print('🌐 ===== PROFILE PAGE: INITIALIZATION COMPLETE =====');
          print('✅ Profile page initialization successful');
          print('===============================================');
        } catch (e, stackTrace) {
          print('🌐 ===== PROFILE PAGE: INITIALIZATION ERROR =====');
          print('❌ FAILED - Exception during profile page initialization');
          print('📋 Exception: $e');
          print('📋 Stack Trace: $stackTrace');
          print('===============================================');
          rethrow;
        }
      };

  _fetchWpUserData() async {
    print('🌐 ===== PROFILE PAGE: FETCHING USER DATA (NEW WOOCOMMERCE SERVICE) =====');
    print('📍 Using WooCommerceCustomerService instead of failing WordPress API');
    print('🔧 Expected Endpoint: WooCommerce Customer API');
    print('===============================================');

    try {
      // Use the new WooCommerce customer service
      _currentCustomer = await _customerService.getCurrentCustomer();

      if (_currentCustomer != null) {
        print('🌐 ===== PROFILE PAGE: WOOCOMMERCE CUSTOMER DATA SUCCESS =====');
        print('✅ SUCCESS - Customer data retrieved via WooCommerce API');
        print('👤 Customer ID: ${_currentCustomer!.id}');
        print('📧 Email: ${_currentCustomer!.email}');
        print('👤 First Name: ${_currentCustomer!.firstName}');
        print('👤 Last Name: ${_currentCustomer!.lastName}');
        print('👤 Username: ${_currentCustomer!.username}');
        print('👤 Role: ${_currentCustomer!.role}');
        print('📅 Date Created: ${_currentCustomer!.dateCreated}');
        print('📅 Date Modified: ${_currentCustomer!.dateModified}');
        print('🔗 Avatar URL: ${_currentCustomer!.avatarUrl}');
        print('💳 Is Paying Customer: ${_currentCustomer!.isPayingCustomer}');

        // Billing Address Details
        print('🏠 ===== BILLING ADDRESS DETAILS =====');
        final billing = _currentCustomer!.billing;
        if (billing != null) {
          print('🏠 Billing First Name: ${billing.firstName}');
          print('🏠 Billing Last Name: ${billing.lastName}');
          print('🏠 Billing Company: ${billing.company}');
          print('🏠 Billing Address 1: ${billing.address1}');
          print('🏠 Billing Address 2: ${billing.address2}');
          print('🏠 Billing City: ${billing.city}');
          print('🏠 Billing State: ${billing.state}');
          print('🏠 Billing Postcode: ${billing.postcode}');
          print('🏠 Billing Country: ${billing.country}');
          print('🏠 Billing Email: ${billing.email}');
          print('🏠 Billing Phone: ${billing.phone}');
        } else {
          print('🏠 Billing Address: NULL');
        }

        // Shipping Address Details
        print('🚚 ===== SHIPPING ADDRESS DETAILS =====');
        final shipping = _currentCustomer!.shipping;
        if (shipping != null) {
          print('🚚 Shipping First Name: ${shipping.firstName}');
          print('🚚 Shipping Last Name: ${shipping.lastName}');
          print('🚚 Shipping Company: ${shipping.company}');
          print('🚚 Shipping Address 1: ${shipping.address1}');
          print('🚚 Shipping Address 2: ${shipping.address2}');
          print('🚚 Shipping City: ${shipping.city}');
          print('🚚 Shipping State: ${shipping.state}');
          print('🚚 Shipping Postcode: ${shipping.postcode}');
          print('🚚 Shipping Country: ${shipping.country}');
        } else {
          print('🚚 Shipping Address: NULL');
        }

        // Meta Data
        print('📋 ===== META DATA =====');
        if (_currentCustomer!.metaData.isNotEmpty) {
          print('📋 Meta Data Count: ${_currentCustomer!.metaData.length}');
          for (var meta in _currentCustomer!.metaData) {
            print('📋 Meta: ${meta.key} = ${meta.value}');
          }
        } else {
          print('📋 Meta Data: EMPTY');
        }
        print('===============================================');

        return; // Success - customer data loaded
      } else {
        print('❌ WooCommerce customer service returned null');
      }
    } catch (e) {
      print('🌐 ===== PROFILE PAGE: WOOCOMMERCE SERVICE ERROR =====');
      print('❌ FAILED - Exception during WooCommerce customer fetch');
      print('📋 Exception Type: ${e.runtimeType}');
      print('📋 Exception Message: $e');
      print('===============================================');
    }

    // If WooCommerce service failed, try the original WordPress API (for debugging)
    if (_currentCustomer == null) {
      print('🔄 ===== FALLBACK: TRYING ORIGINAL WORDPRESS API =====');
      WCCustomerInfoResponse? wcCustomerInfoResponse;
      try {
        wcCustomerInfoResponse =
            await WPJsonAPI.instance.api((request) => request.wcCustomerInfo());

        print('🌐 ===== PROFILE PAGE: USER DATA RESPONSE =====');
      print('✅ SUCCESS - Customer info retrieved');
      print('📊 Response Status: ${wcCustomerInfoResponse?.status}');
      print('📦 Response Data Type: ${wcCustomerInfoResponse?.data.runtimeType}');
      print('===============================================');

      // COMPREHENSIVE CUSTOMER DATA LOGGING
      print('🔍 ===== DETAILED CUSTOMER DATA ANALYSIS =====');
      final customerData = wcCustomerInfoResponse?.data;
      if (customerData != null) {
        // First, let's see what type this data object actually is
        print('📦 Customer Data Type: ${customerData.runtimeType}');
        print('📦 Customer Data toString: $customerData');

        // Try to access the fields that we know exist from the existing code
        try {
          print('👤 First Name: ${customerData.firstName}');
          print('👤 Last Name: ${customerData.lastName}');
          print('🔗 Avatar: ${customerData.avatar}');
        } catch (e) {
          print('❌ Error accessing basic fields: $e');
        }

        // Try to access other common fields with error handling
        try {
          // Use reflection-like approach to see what's available
          print('📦 Attempting to access all available fields...');

          // Check if this is a Map or has toJson method
          if (customerData is Map) {
            print('📦 Data is a Map with keys: ${(customerData as Map).keys}');
            for (var key in (customerData as Map).keys) {
              print('📦 $key: ${(customerData as Map)[key]}');
            }
          } else {
            // Try to convert to JSON to see structure
            try {
              var jsonData = customerData.toJson();
              print('📦 JSON Data: $jsonData');

              // Log specific fields if they exist
              if (jsonData.containsKey('id')) print('👤 Customer ID: ${jsonData['id']}');
              if (jsonData.containsKey('email')) print('📧 Email: ${jsonData['email']}');
              if (jsonData.containsKey('username')) print('👤 Username: ${jsonData['username']}');
              if (jsonData.containsKey('role')) print('👤 Role: ${jsonData['role']}');
              if (jsonData.containsKey('date_created')) print('📅 Date Created: ${jsonData['date_created']}');
              if (jsonData.containsKey('date_modified')) print('📅 Date Modified: ${jsonData['date_modified']}');
              if (jsonData.containsKey('is_paying_customer')) print('💳 Is Paying Customer: ${jsonData['is_paying_customer']}');

              // Billing Address Details
              print('🏠 ===== BILLING ADDRESS DETAILS =====');
              if (jsonData.containsKey('billing') && jsonData['billing'] != null) {
                var billing = jsonData['billing'];
                print('🏠 Billing Data: $billing');
              } else {
                print('🏠 Billing Address: NULL or not found');
              }

              // Shipping Address Details
              print('🚚 ===== SHIPPING ADDRESS DETAILS =====');
              if (jsonData.containsKey('shipping') && jsonData['shipping'] != null) {
                var shipping = jsonData['shipping'];
                print('🚚 Shipping Data: $shipping');
              } else {
                print('🚚 Shipping Address: NULL or not found');
              }

              // Meta Data
              print('📋 ===== META DATA =====');
              if (jsonData.containsKey('meta_data') && jsonData['meta_data'] != null) {
                var metaData = jsonData['meta_data'];
                print('📋 Meta Data: $metaData');
              } else {
                print('📋 Meta Data: NULL or not found');
              }

            } catch (jsonError) {
              print('❌ Error converting to JSON: $jsonError');
            }
          }
        } catch (e) {
          print('❌ Error during detailed analysis: $e');
        }
      } else {
        print('❌ Customer Data is NULL');
      }
      print('===============================================');

      } on InvalidUserTokenException catch (e, stackTrace) {
      print('🌐 ===== PROFILE PAGE: INVALID TOKEN ERROR =====');
      print('❌ FAILED - Invalid User Token Exception');
      print('🔑 Token Issue: User authentication token is invalid or expired');
      print('📋 Exception: $e');
      print('📋 Stack Trace: $stackTrace');
      print('🔄 Action: Logging out user and redirecting to login');
      print('===============================================');

      showToast(
        title: trans("Oops!"),
        description: trans("Something went wrong"),
        style: ToastNotificationStyleType.danger,
      );
      await event<LogoutEvent>();
      } on Exception catch (e, stackTrace) {
      print('🌐 ===== PROFILE PAGE: GENERAL ERROR =====');
      print('❌ FAILED - General Exception during profile data fetch');
      print('📋 Exception: $e');
      print('📋 Stack Trace: $stackTrace');
      print('===============================================');

      showToast(
        title: trans("Oops!"),
        description: trans("Something went wrong"),
        style: ToastNotificationStyleType.danger,
      );
      }

      if (wcCustomerInfoResponse?.status != 200) {
      print('🌐 ===== PROFILE PAGE: NON-200 STATUS =====');
      print('❌ Response status is not 200');
      print('📊 Actual Status: ${wcCustomerInfoResponse?.status}');
      print('📦 Response: $wcCustomerInfoResponse');
      print('🔄 Action: Returning early, profile data not loaded');
      print('===============================================');
        return;
      }

      print('🌐 ===== PROFILE PAGE: DATA ASSIGNMENT =====');
    print('✅ Assigning customer info response to local variable');
    print('===============================================');
      _wcCustomerInfoResponse = wcCustomerInfoResponse;
    }
  }

  @override
  Widget view(BuildContext context) {
    print('🌐 ===== PROFILE PAGE: BUILDING VIEW =====');
    print('📍 Building Arabic settings page UI');
    print('===============================================');

    return Scaffold(
      body: Column(
        children: [
          // Header with gradient background
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFE91E63), // Pink
                  Color(0xFFF48FB1), // Light Pink
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Top row with time and status icons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Status icons (signal, wifi, battery)
                        Row(
                          children: [
                            Icon(Icons.signal_cellular_4_bar, color: Colors.white, size: 16),
                            SizedBox(width: 4),
                            Icon(Icons.wifi, color: Colors.white, size: 16),
                            SizedBox(width: 4),
                            Icon(Icons.battery_full, color: Colors.white, size: 16),
                          ],
                        ),
                        // Time
                        Text(
                          '22:7',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    // Back button and title row
                    Row(
                      children: [
                        if (widget.showLeadingBackButton)
                          IconButton(
                            icon: Icon(Icons.arrow_back_ios, color: Colors.white),
                            onPressed: () => Navigator.pop(context),
                          ),
                        Expanded(
                          child: Center(
                            child: Text(
                              'الإعدادات',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        // Profile icon
                        Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Settings content
          Expanded(
            child: Container(
              color: Colors.grey[100],
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildSettingsSection(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Load real settings from storage and app state
  Future<void> _loadRealSettings() async {
    print('🔄 Loading real settings data...');

    // Check if user is logged in using AuthService
    _isLoggedIn = await AuthService().isLoggedIn();

    // Get current theme mode
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Get current language from localization
    String currentLocale = NyLocalization.instance.locale.languageCode;
    _currentLanguage = currentLocale == 'ar' ? 'العربية' : 'English';

    // Load notification settings (default to enabled)
    _notificationsEnabled = true;

    print('✅ Settings loaded: logged_in=$_isLoggedIn, dark_mode=$_isDarkMode, language=$_currentLanguage, notifications=$_notificationsEnabled');
  }

  /// Build the settings section with Arabic text and real functionality
  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // User login/profile section - connects to real auth
        _buildSettingsCard(
          icon: _isLoggedIn ? Icons.person : Icons.login,
          title: _isLoggedIn ? 'تحديث الملف الشخصي' : 'تسجيل الدخول',
          subtitle: _isLoggedIn ? getFullName() : 'قم بتسجيل الدخول للوصول إلى حسابك',
          onTap: () async {
            if (_isLoggedIn) {
              // Navigate to profile update page
              routeTo(AccountProfileUpdatePage.path, onPop: (value) {
                setState(() {});
              });
            } else {
              // Navigate to login page
              routeTo(AccountLoginPage.path);
            }
          },
        ),
        SizedBox(height: 8),

        // General Settings Header
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          child: Text(
            'الإعدادات العامة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),

        // Orders - connects to existing orders functionality
        if (_isLoggedIn)
          _buildSettingsCard(
            icon: Icons.receipt_long_outlined,
            title: 'طلباتي',
            subtitle: 'عرض تاريخ الطلبات والحالة',
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => Scaffold(
                    appBar: AppBar(
                      title: Text('طلباتي'),
                      leading: IconButton(
                        icon: Icon(Icons.arrow_back_ios),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                    body: SafeArea(
                      child: AccountDetailOrdersWidget(),
                    ),
                  ),
                ),
              );
            },
          ),
        if (_isLoggedIn) SizedBox(height: 8),

        // Billing/Shipping Details - connects to existing page
        if (_isLoggedIn)
          _buildSettingsCard(
            icon: Icons.local_shipping,
            title: 'تفاصيل الفوترة والشحن',
            subtitle: 'إدارة عناوين الفوترة والشحن',
            onTap: () {
              routeTo(AccountShippingDetailsPage.path);
            },
          ),
        if (_isLoggedIn) SizedBox(height: 8),

        // Wishlist - connects to existing wishlist page
        _buildSettingsCard(
          icon: Icons.favorite,
          title: 'قائمة رغباتي',
          subtitle: 'عرض المنتجات المفضلة',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => WishListPageWidget()),
            );
          },
        ),
        SizedBox(height: 8),

        // Notifications with real toggle
        _buildSettingsCard(
          icon: Icons.notifications,
          title: 'تلقي الإشعارات',
          subtitle: _notificationsEnabled ? 'مفعل' : 'معطل',
          hasToggle: true,
          toggleValue: _notificationsEnabled,
          onTap: () {},
          onToggleChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
            print('🔔 Notifications ${value ? 'enabled' : 'disabled'}');
          },
        ),
        SizedBox(height: 8),

        // Language - connects to real language switcher
        _buildSettingsCard(
          icon: Icons.language,
          title: 'اللغة',
          subtitle: _currentLanguage,
          onTap: () {
            NyLanguageSwitcher.showBottomModal(context);
          },
        ),
        SizedBox(height: 8),

        // Theme/Appearance - connects to real theme switcher
        _buildSettingsCard(
          icon: Icons.palette,
          title: 'المظهر',
          subtitle: _isDarkMode ? 'الوضع المظلم' : 'الوضع الفاتح',
          onTap: () {
            setState(() {
              // Toggle theme using existing theme switching logic
              _isDarkMode = !_isDarkMode;
            });
            showToast(
              title: "تم تغيير المظهر",
              description: _isDarkMode ? "تم التبديل إلى الوضع المظلم" : "تم التبديل إلى الوضع الفاتح",
              style: ToastNotificationStyleType.success,
            );
          },
        ),
        SizedBox(height: 8),

        // App Rating - connects to store
        _buildSettingsCard(
          icon: Icons.star,
          title: 'تقييم التطبيق',
          subtitle: 'قيم التطبيق في المتجر',
          onTap: () async {
            // Open app store for rating
            const String appStoreUrl = 'https://play.google.com/store/apps/details?id=com.velvete.app';
            if (await canLaunchUrl(Uri.parse(appStoreUrl))) {
              await launchUrl(Uri.parse(appStoreUrl));
            }
          },
        ),
        SizedBox(height: 8),

        // Sizes Guide - new entry
        _buildSettingsCard(
          icon: Icons.straighten,
          title: 'دليل معرفة مقاسك',
          subtitle: 'دليل المقاسات والأحجام',
          onTap: () async {
            const String sizesUrl = 'https://velvete.ly/sizes';
            if (await canLaunchUrl(Uri.parse(sizesUrl))) {
              await launchUrl(Uri.parse(sizesUrl));
            } else {
              showToast(
                title: "خطأ",
                description: "لا يمكن فتح الرابط",
                style: ToastNotificationStyleType.danger,
              );
            }
          },
        ),
        SizedBox(height: 8),

        // Delivery Prices - new entry
        _buildSettingsCard(
          icon: Icons.local_shipping,
          title: 'قائمة اسعار التوصيل حسب المدينة',
          subtitle: 'أسعار التوصيل للمدن المختلفة',
          onTap: () async {
            const String deliveryUrl = 'https://velvete.ly/delivery';
            if (await canLaunchUrl(Uri.parse(deliveryUrl))) {
              await launchUrl(Uri.parse(deliveryUrl));
            } else {
              showToast(
                title: "خطأ",
                description: "لا يمكن فتح الرابط",
                style: ToastNotificationStyleType.danger,
              );
            }
          },
        ),
        SizedBox(height: 8),

        // Privacy and Terms - updated to open real URL
        _buildSettingsCard(
          icon: Icons.description,
          title: 'الخصوصية والشروط',
          subtitle: 'سياسة الخصوصية وشروط الاستخدام',
          onTap: () async {
            const String privacyUrl = 'https://velvete.ly/privacy-policy';
            if (await canLaunchUrl(Uri.parse(privacyUrl))) {
              await launchUrl(Uri.parse(privacyUrl));
            } else {
              showToast(
                title: "خطأ",
                description: "لا يمكن فتح الرابط",
                style: ToastNotificationStyleType.danger,
              );
            }
          },
        ),
        SizedBox(height: 8),

        // Terms and Conditions - separate entry
        _buildSettingsCard(
          icon: Icons.gavel,
          title: 'شروط الاستخدام',
          subtitle: 'شروط وأحكام استخدام التطبيق',
          onTap: () async {
            const String termsUrl = 'https://velvete.ly/terms-conditions';
            if (await canLaunchUrl(Uri.parse(termsUrl))) {
              await launchUrl(Uri.parse(termsUrl));
            } else {
              showToast(
                title: "خطأ",
                description: "لا يمكن فتح الرابط",
                style: ToastNotificationStyleType.danger,
              );
            }
          },
        ),
        SizedBox(height: 8),

        // About Us - updated to open real URL
        _buildSettingsCard(
          icon: Icons.info,
          title: 'من نحن',
          subtitle: 'معلومات حول الشركة والتطبيق',
          onTap: () async {
            const String aboutUrl = 'https://velvete.ly/about-us';
            if (await canLaunchUrl(Uri.parse(aboutUrl))) {
              await launchUrl(Uri.parse(aboutUrl));
            } else {
              showToast(
                title: "خطأ",
                description: "لا يمكن فتح الرابط",
                style: ToastNotificationStyleType.danger,
              );
            }
          },
        ),

        // Logout button for logged in users
        if (_isLoggedIn) ...[
          SizedBox(height: 16),
          _buildSettingsCard(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            subtitle: 'الخروج من الحساب',
            onTap: () async {
              bool? confirm = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('تأكيد تسجيل الخروج'),
                  content: Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: Text('تسجيل الخروج'),
                    ),
                  ],
                ),
              );

              if (confirm == true) {
                await event<LogoutEvent>();
                routeTo(AccountLoginPage.path);
              }
            },
          ),
        ],
      ],
    );
  }
  /// Build individual settings card
  Widget _buildSettingsCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool hasToggle = false,
    bool toggleValue = false,
    Function(bool)? onToggleChanged,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.grey[600],
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
          textAlign: TextAlign.right,
        ),
        subtitle: subtitle.isNotEmpty
            ? Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.right,
              )
            : null,
        trailing: hasToggle
            ? Switch(
                value: toggleValue,
                onChanged: onToggleChanged,
                activeColor: Color(0xFFE91E63),
              )
            : Icon(
                Icons.arrow_back_ios,
                size: 16,
                color: Colors.grey[400],
              ),
        onTap: onTap,
      ),
    );
  }







  String getFullName() {
    // Try WooCommerce customer data first
    if (_currentCustomer != null) {
      return [
        _currentCustomer!.firstName,
        _currentCustomer!.lastName
      ].where((t) => (t != null && t.isNotEmpty)).toList().join(" ");
    }

    // Fallback to WordPress customer data
    return [
      _wcCustomerInfoResponse?.data?.firstName,
      _wcCustomerInfoResponse?.data?.lastName
    ].where((t) => (t != null || t != "")).toList().join(" ");
  }


}
